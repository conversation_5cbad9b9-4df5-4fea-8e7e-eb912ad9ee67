import { defineCollection, z } from 'astro:content';

const portfolioCollection = defineCollection({
  type: 'content',
  schema: z.object({
    title: z.string(),
    description: z.string(),
    publishDate: z.date(),
    image: z.string().optional(),
    technologies: z.array(z.string()),
    tags: z.array(z.string()),
    github: z.string().optional(),
    live: z.string().optional(),
    featured: z.boolean().default(false),
  }),
});

const resourcesCollection = defineCollection({
  type: 'content',
  schema: z.object({
    title: z.string(),
    description: z.string(),
    url: z.string(),
    category: z.string(),
    tags: z.array(z.string()),
    featured: z.boolean().default(false),
    image: z.string().optional(), // Optional image override
  }),
});

const aboutCollection = defineCollection({
  type: 'content',
  schema: z.object({
    title: z.string(),
    description: z.string().optional(),
    updatedDate: z.date().optional(),
    sections: z.any().optional(),
  }),
});

const homepageCollection = defineCollection({
  type: 'content',
  schema: z.object({
    title: z.string(),
    lastUpdated: z.date().optional(),
    sections: z.any().optional(),
    hero: z.any().optional(),
    about: z.any().optional(),
  }),
});

const contactCollection = defineCollection({
  type: 'content',
  schema: z.object({
    title: z.string(),
    lastUpdated: z.date().optional(),
    heading: z.string(),
    subheading: z.string(),
    sections: z.any().optional(),
    buttons: z.any().optional(),
    form: z.any().optional(),
  }),
});

export const collections = {
  portfolio: portfolioCollection,
  resources: resourcesCollection,
  about: aboutCollection,
  homepage: homepageCollection,
  contact: contactCollection,
};