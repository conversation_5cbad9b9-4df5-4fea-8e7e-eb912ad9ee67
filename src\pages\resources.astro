---
import Layout from '../layouts/Layout.astro';
import { getCollection } from 'astro:content';
import { siteConfig } from '../config/site';
import { getResourceImageSet } from '../utils/image-extractor';

const resources = await getCollection('resources');
const categories = [...new Set(resources.map(resource => resource.data.category))];
const allTags = [...new Set(resources.flatMap(resource => resource.data.tags))];

// Pre-process resources to include optimized images for different sizes
const resourcesWithImages = await Promise.all(
  resources.map(async (resource) => {
    const imageSet = await getResourceImageSet(
      resource.data.url,
      resource.data.title,
      resource.data.category,
      resource.data.image
    );

    return {
      ...resource,
      images: imageSet
    };
  })
);
---

<Layout title={`Resources | ${siteConfig.title}`}>
  <section class="resources section bg-gradient-to-br from-background-light via-background-light-secondary to-secondary-50/40 dark:from-background-dark dark:via-background-dark-secondary dark:to-secondary-950/40 relative overflow-hidden">
    <!-- Modern background elements matching other pages -->
    <div class="absolute inset-0 pointer-events-none">
      <div class="absolute top-20 left-10 w-72 h-72 bg-gradient-to-br from-primary-400/5 to-transparent rounded-full blur-3xl"></div>
      <div class="absolute bottom-20 right-10 w-72 h-72 bg-gradient-to-tl from-accent-400/5 to-transparent rounded-full blur-3xl"></div>
    </div>

    <div class="container-custom relative z-10">
      <div class="text-center mb-16">
        <h1 class="heading-xl text-secondary-800 dark:text-secondary-200 mb-6 relative">
          Resources & Knowledge Hub
          <span class="absolute bottom-0 left-1/2 transform -translate-x-1/2 -mb-2 w-20 h-1 bg-gradient-to-r from-primary-500 to-accent-500 rounded"></span>
        </h1>
        <p class="text-lg text-secondary-600 dark:text-secondary-400 max-w-2xl mx-auto leading-relaxed">
          Curated collection of tools, articles, and resources for developers and tech enthusiasts
        </p>
      </div>
      
      <!-- Modern Floating Controls Bar - 2025 Design -->
      <div class="mb-12">
        <!-- Floating Glassmorphism Control Bar -->
        <div class="sticky top-6 z-30 mb-8">
          <div class="backdrop-blur-xl bg-white/80 dark:bg-secondary-900/80 border border-secondary-200/50 dark:border-secondary-700/50 rounded-2xl shadow-2xl shadow-secondary-900/10 dark:shadow-black/20 p-4">
            <div class="flex flex-col lg:flex-row gap-4 lg:gap-6 items-stretch lg:items-center">
              
              <!-- Direct Search Input -->
              <div class="flex-1 relative">
                <div class="relative">
                  <svg class="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-secondary-400 dark:text-secondary-500 pointer-events-none" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                  </svg>
                  <input 
                    type="text" 
                    id="search-input"
                    placeholder="Search resources, tools, frameworks..."
                    class="w-full pl-12 pr-4 py-4 bg-secondary-50/80 dark:bg-secondary-800/80 hover:bg-secondary-100/80 dark:hover:bg-secondary-700/80 focus:bg-white dark:focus:bg-secondary-800 border border-secondary-200/60 dark:border-secondary-600/60 focus:border-primary-500/50 dark:focus:border-primary-400/50 rounded-xl transition-all duration-200 focus:ring-2 focus:ring-primary-500/50 focus:outline-none text-secondary-700 dark:text-secondary-300 placeholder-secondary-500 dark:placeholder-secondary-400"
                  />
                  <button 
                    type="button" 
                    id="clear-search-btn" 
                    class="absolute right-3 top-1/2 transform -translate-y-1/2 p-1 text-secondary-400 hover:text-secondary-600 dark:hover:text-secondary-300 transition-colors rounded opacity-0 invisible"
                    aria-label="Clear search"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                  </button>
                </div>
              </div>

              <div class="flex flex-col sm:flex-row gap-3 lg:gap-4">
                <!-- Modern Sort Dropdown -->
                <div class="relative">
                  <button 
                    type="button"
                    id="sort-trigger" 
                    class="flex items-center gap-3 px-4 py-4 min-w-[180px] text-left bg-secondary-50/80 dark:bg-secondary-800/80 hover:bg-secondary-100/80 dark:hover:bg-secondary-700/80 border border-secondary-200/60 dark:border-secondary-600/60 rounded-xl transition-all duration-200 group focus:ring-2 focus:ring-primary-500/50 focus:border-primary-500/50"
                    aria-label="Sort options"
                  >
                    <svg class="w-4 h-4 text-secondary-500 dark:text-secondary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4h13M3 8h9m-9 4h9m5-4v12m0 0l-4-4m4 4l4-4"></path>
                    </svg>
                    <span class="flex-1 text-secondary-700 dark:text-secondary-300 font-medium text-sm">Sort by Title</span>
                    <svg class="w-4 h-4 text-secondary-400 dark:text-secondary-500 group-hover:text-secondary-600 dark:group-hover:text-secondary-400 transition-all duration-200 group-hover:rotate-180" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                  </button>
                  
                  <!-- Sort Dropdown Menu -->
                  <div id="sort-menu" class="hidden absolute top-full left-0 right-0 mt-2 backdrop-blur-xl bg-white/95 dark:bg-secondary-900/95 border border-secondary-200/50 dark:border-secondary-700/50 rounded-xl shadow-2xl shadow-secondary-900/20 dark:shadow-black/40 py-2 z-50">
                    <button type="button" class="sort-option w-full px-4 py-3 text-left hover:bg-secondary-50 dark:hover:bg-secondary-800/80 transition-colors duration-150 flex items-center gap-3 text-sm" data-value="title">
                      <div class="w-4 h-4 flex items-center justify-center">
                        <div class="w-2 h-2 bg-primary-500 rounded-full opacity-100"></div>
                      </div>
                      <span class="text-secondary-900 dark:text-secondary-100 font-medium">Sort by Title</span>
                    </button>
                    <button type="button" class="sort-option w-full px-4 py-3 text-left hover:bg-secondary-50 dark:hover:bg-secondary-800/80 transition-colors duration-150 flex items-center gap-3 text-sm" data-value="category">
                      <div class="w-4 h-4 flex items-center justify-center">
                        <div class="w-2 h-2 bg-primary-500 rounded-full opacity-0"></div>
                      </div>
                      <span class="text-secondary-700 dark:text-secondary-300">Category</span>
                    </button>
                    <button type="button" class="sort-option w-full px-4 py-3 text-left hover:bg-secondary-50 dark:hover:bg-secondary-800/80 transition-colors duration-150 flex items-center gap-3 text-sm" data-value="recent">
                      <div class="w-4 h-4 flex items-center justify-center">
                        <div class="w-2 h-2 bg-primary-500 rounded-full opacity-0"></div>
                      </div>
                      <span class="text-secondary-700 dark:text-secondary-300">Recently Added</span>
                    </button>
                  </div>
                </div>

                <!-- Animated Segmented Control -->
                <div class="relative bg-secondary-50/80 dark:bg-secondary-800/80 border border-secondary-200/60 dark:border-secondary-600/60 rounded-xl p-1">
                  <!-- Sliding Active Indicator -->
                  <div id="view-indicator" class="absolute top-1 left-1 w-[calc(50%-2px)] h-[calc(100%-8px)] bg-primary-500 rounded-lg transition-transform duration-300 ease-out shadow-lg"></div>
                  
                  <div class="relative flex">
                    <button 
                      type="button" 
                      id="grid-view-btn" 
                      class="relative z-10 flex items-center justify-center gap-2 px-4 py-3 text-sm font-medium transition-colors duration-200 text-white"
                      aria-label="Grid view"
                    >
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"></path>
                      </svg>
                      <span class="hidden sm:inline">Cards</span>
                    </button>
                    <button
                      type="button"
                      id="list-view-btn"
                      class="relative z-10 flex items-center justify-center gap-2 px-4 py-3 text-sm font-medium transition-colors duration-200 text-secondary-600 dark:text-secondary-400"
                      aria-label="Magazine view"
                    >
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                      </svg>
                      <span class="hidden sm:inline">Magazine</span>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Search Command Palette Modal -->
        <div id="search-modal" class="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 hidden opacity-0 transition-opacity duration-300">
          <div class="flex items-start justify-center min-h-screen pt-16 px-4">
            <div class="w-full max-w-2xl backdrop-blur-xl bg-white/95 dark:bg-secondary-900/95 border border-secondary-200/50 dark:border-secondary-700/50 rounded-2xl shadow-2xl shadow-black/25 overflow-hidden transform scale-95 transition-transform duration-300">
              <!-- Search Input -->
              <div class="relative border-b border-secondary-200/50 dark:border-secondary-700/50">
                <svg class="absolute left-6 top-1/2 transform -translate-y-1/2 w-5 h-5 text-secondary-400 dark:text-secondary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
                <input 
                  type="text" 
                  id="modal-search-input"
                  placeholder="Search resources, tools, frameworks..." 
                  class="w-full pl-14 pr-6 py-6 text-lg bg-transparent border-0 outline-none text-secondary-900 dark:text-secondary-100 placeholder-secondary-500 dark:placeholder-secondary-400"
                  autocomplete="off"
                >
              </div>
              
              <!-- Search Results Area -->
              <div class="max-h-96 overflow-y-auto p-2">
                <div id="search-results" class="space-y-1">
                  <!-- Dynamic search results will be inserted here -->
                </div>
                <div id="no-search-results" class="hidden text-center py-12 text-secondary-500 dark:text-secondary-400">
                  <svg class="w-12 h-12 mx-auto mb-4 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                  </svg>
                  <p class="text-sm">No resources found</p>
                </div>
              </div>
            </div>
          </div>
        </div>
        
      </div>

      <!-- Main Content Layout with Sidebar -->
      <div class="flex flex-col lg:flex-row gap-8">
        <!-- Left Sidebar - Categories -->
        <aside class="lg:w-64 flex-shrink-0 sidebar-container">
          <div class="bg-white/80 dark:bg-secondary-900/80 backdrop-blur-xl border border-secondary-200/50 dark:border-secondary-700/50 rounded-2xl p-6 sticky top-24 shadow-lg shadow-secondary-900/5 dark:shadow-black/20">
            <h3 class="text-lg font-bold text-secondary-900 dark:text-secondary-100 mb-4 flex items-center gap-2">
              <svg class="w-5 h-5 text-primary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
              </svg>
              Categories
            </h3>
            <nav class="space-y-2">
              <button class="filter-btn active w-full flex items-center justify-between px-4 py-3 bg-gradient-to-r from-primary-500 to-primary-600 text-white rounded-xl hover:from-primary-600 hover:to-primary-700 transition-all duration-300 text-sm font-semibold shadow-lg shadow-primary-500/25" data-filter="all">
                <div class="flex items-center gap-3">
                  <div class="w-2 h-2 bg-white rounded-full opacity-90"></div>
                  <span>All Resources</span>
                </div>
                <span class="text-xs bg-white/20 text-white px-2 py-0.5 rounded-full font-medium">{resourcesWithImages.length}</span>
              </button>
              {categories.map(category => {
                const count = resourcesWithImages.filter(r => r.data.category === category).length;
                return (
                  <button class={`filter-btn w-full flex items-center justify-between px-4 py-3 text-secondary-700 dark:text-secondary-300 hover:bg-secondary-50 dark:hover:bg-secondary-800/50 rounded-xl transition-all duration-300 text-sm font-medium group`} data-filter={category.toLowerCase()}>
                    <div class="flex items-center gap-3">
                      <div class={`w-2 h-2 rounded-full transition-colors duration-300 ${
                        category.toLowerCase() === 'documentation' ? 'bg-blue-500 group-hover:bg-blue-600' :
                        category.toLowerCase() === 'design tool' ? 'bg-orange-500 group-hover:bg-orange-600' :
                        category.toLowerCase() === 'development tool' ? 'bg-emerald-500 group-hover:bg-emerald-600' :
                        category.toLowerCase() === 'css framework' ? 'bg-purple-500 group-hover:bg-purple-600' :
                        category.toLowerCase() === 'learning resource' ? 'bg-pink-500 group-hover:bg-pink-600' :
                        'bg-secondary-400 group-hover:bg-secondary-500'
                      }`}></div>
                      <span class="group-hover:text-secondary-900 dark:group-hover:text-secondary-100 transition-colors duration-300">{category}</span>
                    </div>
                    <span class="text-xs bg-secondary-100 dark:bg-secondary-800 text-secondary-600 dark:text-secondary-400 px-2 py-0.5 rounded-full font-medium group-hover:bg-secondary-200 dark:group-hover:bg-secondary-700 transition-colors duration-300">{count}</span>
                  </button>
                );
              })}
            </nav>
          </div>
        </aside>

        <!-- Main Content Area -->
        <main class="flex-1 min-w-0 main-content-grid">
          <!-- Resources Grid -->
          <div id="resources-container" class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4 md:gap-5">
            {resourcesWithImages.map((resource, index) => (
              <article class="resource-card group card-interactive relative overflow-hidden bg-white dark:bg-secondary-800 border border-secondary-200/60 dark:border-secondary-700/60 rounded-xl transition-all duration-300 hover:border-primary-400/80 dark:hover:border-primary-500/80 hover:shadow-lg hover:shadow-secondary-900/5 dark:hover:shadow-black/20 hover:-translate-y-1" data-category={resource.data.category.toLowerCase()} data-tags={resource.data.tags.join(',').toLowerCase()} data-title={resource.data.title.toLowerCase()} role="listitem">

                <!-- Card view layout (default) -->
                <div class="card-layout flex flex-col h-full">
                  <!-- Large preview image section -->
                  <div class="relative aspect-[4/3] bg-gradient-to-br from-gray-50 to-gray-100 dark:from-secondary-900 dark:to-secondary-800 overflow-hidden rounded-t-xl">
                    <!-- Optimized resource image with responsive loading -->
                    <img
                      src={resource.images.large}
                      srcset={`${resource.images.medium} 1x, ${resource.images.large} 2x`}
                      alt={`${resource.data.title} preview`}
                      class="w-full h-full object-cover transition-all duration-500 ease-out group-hover:scale-[1.02]"
                      loading="lazy"
                      decoding="async"
                      onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';"
                    />

                    <!-- Fallback icon (hidden by default, shown if image fails) -->
                    <div class="absolute inset-0 flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100 dark:from-secondary-900 dark:to-secondary-800" style="display: none;">
                      <!-- Category icon -->
                      <div class="flex items-center justify-center">
                        <div class={`w-20 h-20 rounded-xl flex items-center justify-center text-white font-bold text-2xl shadow-lg ${
                          resource.data.category.toLowerCase() === 'documentation' ? 'bg-gradient-to-br from-blue-500 to-indigo-600' :
                          resource.data.category.toLowerCase() === 'tools' ? 'bg-gradient-to-br from-emerald-500 to-teal-600' :
                          resource.data.category.toLowerCase() === 'learning' ? 'bg-gradient-to-br from-purple-500 to-pink-600' :
                          resource.data.category.toLowerCase() === 'design' ? 'bg-gradient-to-br from-orange-500 to-red-600' :
                          'bg-gradient-to-br from-gray-500 to-slate-600'
                        }`}>
                          {resource.data.category.toLowerCase() === 'documentation' ? (
                            <svg class="w-10 h-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                            </svg>
                          ) : resource.data.category.toLowerCase() === 'tools' ? (
                            <svg class="w-10 h-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                            </svg>
                          ) : resource.data.category.toLowerCase() === 'learning' ? (
                            <svg class="w-10 h-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
                            </svg>
                          ) : resource.data.category.toLowerCase() === 'design' ? (
                            <svg class="w-10 h-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z"/>
                            </svg>
                          ) : (
                            <svg class="w-10 h-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                            </svg>
                          )}
                        </div>
                      </div>
                    </div>

                    <!-- Category badge -->
                    <div class="absolute top-4 left-4 z-10">
                      <span class="px-3 py-1.5 text-xs font-semibold bg-white/95 dark:bg-secondary-900/95 text-secondary-700 dark:text-secondary-200 rounded-full backdrop-blur-sm border border-white/60 dark:border-secondary-600/60 shadow-sm">
                        {resource.data.category}
                      </span>
                    </div>
                  </div>

                  <!-- Card content section -->
                  <div class="flex-1 p-5 flex flex-col">
                    <!-- Title and source -->
                    <div class="mb-3">
                      <h3 class="text-lg font-bold text-text-light dark:text-text-dark group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-all duration-300 mb-1 line-clamp-2">
                        <a href={resource.data.url} target="_blank" rel="noopener" class="hover:underline relative z-10">
                          {resource.data.title}
                        </a>
                      </h3>
                      <p class="text-sm text-secondary-500 dark:text-secondary-400 font-medium">
                        {new URL(resource.data.url).hostname.replace('www.', '')}
                      </p>
                    </div>

                    <!-- Description -->
                    <p class="text-sm text-text-light-muted dark:text-text-dark-muted leading-relaxed mb-4 line-clamp-2 flex-1">
                      {resource.data.description}
                    </p>

                    <!-- Tags -->
                    <div class="flex flex-wrap gap-1.5 mb-4">
                      {resource.data.tags.slice(0, 3).map((tag: string) => (
                        <span class="px-2.5 py-1 text-xs font-medium bg-secondary-100 dark:bg-secondary-700/50 text-secondary-700 dark:text-secondary-300 rounded-full">
                          {tag}
                        </span>
                      ))}
                    </div>


                  </div>
                </div>

                <!-- Magazine view layout (hidden by default) -->
                <div class="magazine-view hidden flex-col md:flex-row h-48 md:h-40 bg-white dark:bg-secondary-800 rounded-xl border border-secondary-200/60 dark:border-secondary-700/60 overflow-hidden transition-all duration-300 hover:border-primary-400/80 dark:hover:border-primary-500/80 hover:shadow-lg hover:shadow-secondary-900/5 dark:hover:shadow-black/20">
                  <!-- Magazine-style image section -->
                  <div class="md:w-48 lg:w-52 flex-shrink-0">
                    <div class="relative h-full bg-gradient-to-br from-gray-50 to-gray-100 dark:from-secondary-900 dark:to-secondary-800 overflow-hidden">
                      <!-- Optimized resource image for magazine view -->
                      <img
                        src={resource.images.large}
                        srcset={`${resource.images.medium} 1x, ${resource.images.large} 2x`}
                        alt={`${resource.data.title} preview`}
                        class="w-full h-full object-cover transition-all duration-500 ease-out group-hover:scale-[1.02]"
                        loading="lazy"
                        decoding="async"
                        onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';"
                      />

                      <!-- Fallback icon (hidden by default, shown if image fails) -->
                      <div class="absolute inset-0 flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100 dark:from-secondary-900 dark:to-secondary-800" style="display: none;">
                        <!-- Category icon -->
                        <div class="flex items-center justify-center">
                          <div class={`w-20 h-20 rounded-xl flex items-center justify-center text-white font-bold text-2xl shadow-lg ${
                            resource.data.category.toLowerCase() === 'documentation' ? 'bg-gradient-to-br from-blue-500 to-indigo-600' :
                            resource.data.category.toLowerCase() === 'tools' ? 'bg-gradient-to-br from-emerald-500 to-teal-600' :
                            resource.data.category.toLowerCase() === 'learning' ? 'bg-gradient-to-br from-purple-500 to-pink-600' :
                            resource.data.category.toLowerCase() === 'design' ? 'bg-gradient-to-br from-orange-500 to-red-600' :
                            'bg-gradient-to-br from-gray-500 to-slate-600'
                          }`}>
                            {resource.data.category.toLowerCase() === 'documentation' ? (
                              <svg class="w-10 h-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                              </svg>
                            ) : resource.data.category.toLowerCase() === 'tools' ? (
                              <svg class="w-10 h-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                              </svg>
                            ) : resource.data.category.toLowerCase() === 'learning' ? (
                              <svg class="w-10 h-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
                              </svg>
                            ) : resource.data.category.toLowerCase() === 'design' ? (
                              <svg class="w-10 h-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z"/>
                              </svg>
                            ) : (
                              <svg class="w-10 h-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                              </svg>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Content section -->
                  <div class="flex-1 p-4 flex flex-col justify-between">
                    <!-- Header section with category and title -->
                    <div class="flex items-start justify-between mb-2">
                      <div class="flex-1">
                        <!-- Category badge -->
                        <div class="mb-1.5">
                          <span class={`inline-flex items-center px-2 py-0.5 text-xs font-medium rounded-full ${
                            resource.data.category.toLowerCase() === 'documentation' ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300' :
                            resource.data.category.toLowerCase() === 'tools' ? 'bg-emerald-100 dark:bg-emerald-900/30 text-emerald-700 dark:text-emerald-300' :
                            resource.data.category.toLowerCase() === 'learning' ? 'bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300' :
                            resource.data.category.toLowerCase() === 'design' ? 'bg-orange-100 dark:bg-orange-900/30 text-orange-700 dark:text-orange-300' :
                            'bg-secondary-100 dark:bg-secondary-700/50 text-secondary-700 dark:text-secondary-300'
                          }`}>
                            {resource.data.category}
                          </span>
                        </div>

                        <!-- Title -->
                        <h3 class="text-lg font-bold text-text-light dark:text-text-dark group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-all duration-300 mb-1 line-clamp-1">
                          <a href={resource.data.url} target="_blank" rel="noopener" class="hover:underline relative z-10">
                            {resource.data.title}
                          </a>
                        </h3>

                        <!-- Source URL -->
                        <p class="text-xs text-secondary-500 dark:text-secondary-400 font-medium">
                          {new URL(resource.data.url).hostname.replace('www.', '')}
                        </p>
                      </div>

                      <!-- Action buttons -->
                      <div class="flex items-center gap-1.5 ml-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <button class="p-1.5 rounded-lg bg-secondary-100 dark:bg-secondary-700 hover:bg-secondary-200 dark:hover:bg-secondary-600 transition-colors duration-200" title="Bookmark">
                          <svg class="w-3.5 h-3.5 text-secondary-600 dark:text-secondary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"/>
                          </svg>
                        </button>
                        <button class="share-btn p-1.5 rounded-lg bg-secondary-100 dark:bg-secondary-700 hover:bg-secondary-200 dark:hover:bg-secondary-600 transition-colors duration-200" title="Share" data-url={resource.data.url} data-title={resource.data.title}>
                          <svg class="w-3.5 h-3.5 text-secondary-600 dark:text-secondary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"/>
                          </svg>
                        </button>
                      </div>
                    </div>

                    <!-- Description -->
                    <p class="text-sm text-text-light dark:text-text-dark leading-relaxed line-clamp-2">
                      {resource.data.description}
                    </p>




                  </div>
                </div>
              </article>
            ))}
          </div>

          <!-- No Results Message -->
          <div id="no-results" class="hidden text-center py-12">
            <div class="w-16 h-16 bg-secondary-100 dark:bg-secondary-800 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-secondary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
              </svg>
            </div>
            <h3 class="text-xl font-semibold text-secondary-800 dark:text-secondary-200 mb-2">No resources found</h3>
            <p class="text-secondary-600 dark:text-secondary-400">Try adjusting your search or filter criteria</p>
          </div>
        </main>
      </div>
    </div>
  </section>
</Layout>

<style>
  /* Line clamping utilities */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* Card layout styles (default) */
  .resource-card {
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    height: auto;
    min-height: 400px;
  }

  .resource-card:hover {
    transform: translateY(-4px);
    box-shadow:
      0 20px 64px -12px rgba(158, 122, 104, 0.25),
      0 8px 32px -8px rgba(45, 42, 35, 0.1),
      0 0 0 1px rgba(158, 122, 104, 0.1);
  }

  /* Magazine layout styles */
  .magazine-layout .resource-card {
    min-height: auto;
  }

  .magazine-layout .resource-card .card-layout {
    display: none;
  }

  .magazine-layout .resource-card .magazine-view {
    display: flex;
  }


  /* Sophisticated dark mode adjustments */
  .dark .resource-card {
    border-color: rgba(232, 230, 225, 0.08);
  }

  .dark .resource-card:hover {
    border-color: rgba(158, 122, 104, 0.3);
    box-shadow: 
      0 20px 64px -12px rgba(0, 0, 0, 0.4),
      0 8px 32px -8px rgba(158, 122, 104, 0.15),
      0 0 0 1px rgba(158, 122, 104, 0.2);
  }

  /* Smooth transitions for all elements */
  .resource-card,
  .resource-card * {
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Image optimization styles */
  .resource-card img {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
    image-rendering: optimize-contrast;
    backface-visibility: hidden;
    transform: translateZ(0);
  }

  /* Improved image loading states */
  .resource-card img[loading="lazy"] {
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
  }

  .resource-card img[loading="lazy"].loaded {
    opacity: 1;
  }

  /* Enhanced responsiveness for magazine layout */
  @media (max-width: 768px) {
    .magazine-layout .resource-card {
      min-height: auto;
    }

    .magazine-layout .resource-card:hover {
      transform: translateY(-2px);
    }

    .magazine-layout .resource-card .flex-col {
      flex-direction: column;
    }

    .magazine-layout .resource-card .md\\:w-48 {
      width: 100%;
    }
  }

  /* High-DPI display optimizations */
  @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .resource-card img {
      image-rendering: -webkit-optimize-contrast;
    }
  }

  /* Ensure proper card spacing and appearance */
  .resource-card .resource-content {
    position: relative;
    z-index: 1;
  }

  /* Enhanced share button styling */
  .resource-card .share-btn {
    border-radius: 0.5rem;
    transition: all 0.3s ease;
  }

  .resource-card .share-btn:hover {
    transform: scale(1.1);
    background: rgba(255, 255, 255, 0.1);
  }

  /* Sidebar responsive styles */
  @media (max-width: 1023px) {
    .sidebar-container {
      position: relative;
      width: 100%;
      margin-bottom: 2rem;
    }

    .sidebar-container .sticky {
      position: relative;
      top: auto;
    }
  }

  /* Improved grid layout for sidebar */
  @media (min-width: 1024px) {
    .main-content-grid {
      max-width: none;
    }
  }

  /* Prevent text selection on filter buttons */
  .filter-btn {
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    outline: none;
  }

  .filter-btn:focus {
    outline: none;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
  }
</style>

<script is:inline>
  // Enhanced Resources Page Functionality
  class ResourcesManager {
    constructor() {
      this.resources = [];
      this.filteredResources = [];
      this.currentFilter = 'all';
      this.currentSort = 'title';
      this.searchQuery = '';
      this.isGridView = true; // Cards view is default
      
      this.init();
    }
    
    init() {
      this.cacheResources();
      this.bindEvents();
    }
    
    cacheResources() {
      const resourceCards = document.querySelectorAll('.resource-card');
      this.resources = Array.from(resourceCards).map(card => ({
        element: card,
        title: card.dataset.title || '',
        category: card.dataset.category || '',
        tags: card.dataset.tags ? card.dataset.tags.split(',') : [],
        url: card.querySelector('a[href]')?.href || ''
      }));
      this.filteredResources = [...this.resources];
    }
    
    bindEvents() {
      // Direct Search Input (main search bar)
      const searchInput = document.getElementById('search-input');
      const clearSearchBtn = document.getElementById('clear-search-btn');
      
      if (searchInput && clearSearchBtn) {
        let searchTimeout;
        
        // Real-time search as user types
        searchInput.addEventListener('input', (e) => {
          clearTimeout(searchTimeout);
          const query = e.target.value.toLowerCase().trim();
          
          // Show/hide clear button
          if (query) {
            clearSearchBtn.classList.remove('opacity-0', 'invisible');
            clearSearchBtn.classList.add('opacity-100', 'visible');
          } else {
            clearSearchBtn.classList.remove('opacity-100', 'visible');
            clearSearchBtn.classList.add('opacity-0', 'invisible');
          }
          
          // Debounced search with shorter delay for better responsiveness
          searchTimeout = setTimeout(() => {
            this.searchQuery = query;
            this.filterAndSort();
          }, 150);
        });
        
        // Clear search functionality
        clearSearchBtn.addEventListener('click', () => {
          searchInput.value = '';
          this.searchQuery = '';
          clearSearchBtn.classList.remove('opacity-100', 'visible');
          clearSearchBtn.classList.add('opacity-0', 'invisible');
          this.filterAndSort();
          searchInput.focus();
        });
      }

      // Modern Search Command Palette
      const searchTrigger = document.getElementById('search-trigger');
      const searchModal = document.getElementById('search-modal');
      const modalSearchInput = document.getElementById('modal-search-input');
      
      if (searchTrigger && searchModal && modalSearchInput) {
        // Open search modal
        const openSearchModal = () => {
          searchModal.classList.remove('hidden');
          setTimeout(() => {
            searchModal.classList.remove('opacity-0');
            searchModal.querySelector('.transform').classList.remove('scale-95');
            modalSearchInput.focus();
          }, 10);
        };

        searchTrigger.addEventListener('click', openSearchModal);

        // Close search modal
        const closeSearchModal = () => {
          searchModal.classList.add('opacity-0');
          searchModal.querySelector('.transform').classList.add('scale-95');
          setTimeout(() => {
            searchModal.classList.add('hidden');
            modalSearchInput.value = '';
            // Also clear main search input and hide clear button
            if (searchInput) {
              searchInput.value = '';
              if (clearSearchBtn) {
                clearSearchBtn.classList.remove('opacity-100', 'visible');
                clearSearchBtn.classList.add('opacity-0', 'invisible');
              }
            }
            this.searchQuery = '';
            this.filterAndSort();
          }, 300);
        };

        // Close on escape or backdrop click
        document.addEventListener('keydown', (e) => {
          if (e.key === 'Escape' && !searchModal.classList.contains('hidden')) {
            closeSearchModal();
          }
        });

        searchModal.addEventListener('click', (e) => {
          if (e.target === searchModal) {
            closeSearchModal();
          }
        });

        // Search functionality with debounce
        let modalSearchTimeout;
        modalSearchInput.addEventListener('input', (e) => {
          clearTimeout(modalSearchTimeout);
          const query = e.target.value.toLowerCase().trim();
          
          // Sync with main search input
          if (searchInput) {
            searchInput.value = e.target.value;
            // Show/hide clear button based on query
            if (clearSearchBtn) {
              if (query) {
                clearSearchBtn.classList.remove('opacity-0', 'invisible');
                clearSearchBtn.classList.add('opacity-100', 'visible');
              } else {
                clearSearchBtn.classList.remove('opacity-100', 'visible');
                clearSearchBtn.classList.add('opacity-0', 'invisible');
              }
            }
          }
          
          modalSearchTimeout = setTimeout(() => {
            this.searchQuery = query;
            this.filterAndSort();
          }, 200);
        });
      }

      // Modern Sort Dropdown
      const sortTrigger = document.getElementById('sort-trigger');
      const sortMenu = document.getElementById('sort-menu');
      
      if (sortTrigger && sortMenu) {
        sortTrigger.addEventListener('click', (e) => {
          e.stopPropagation();
          sortMenu.classList.toggle('hidden');
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', () => {
          sortMenu.classList.add('hidden');
        });

        // Sort option selection
        document.querySelectorAll('.sort-option').forEach(option => {
          option.addEventListener('click', (e) => {
            e.stopPropagation();
            
            // Update active state
            document.querySelectorAll('.sort-option .w-2').forEach(dot => {
              dot.classList.remove('opacity-100');
              dot.classList.add('opacity-0');
            });
            option.querySelector('.w-2').classList.remove('opacity-0');
            option.querySelector('.w-2').classList.add('opacity-100');
            
            // Update button text and sort
            const sortText = option.querySelector('span').textContent;
            sortTrigger.querySelector('span').textContent = sortText;
            this.currentSort = option.dataset.value;
            this.filterAndSort();
            
            sortMenu.classList.add('hidden');
          });
        });
      }

      // Animated Segmented Control
      const gridViewBtn = document.getElementById('grid-view-btn');
      const listViewBtn = document.getElementById('list-view-btn');
      const viewIndicator = document.getElementById('view-indicator');
      
      if (gridViewBtn && listViewBtn && viewIndicator) {
        const setActiveView = (isGrid) => {
          if (isGrid) {
            viewIndicator.style.transform = 'translateX(0%)';
            gridViewBtn.classList.remove('text-secondary-600', 'dark:text-secondary-400');
            gridViewBtn.classList.add('text-white');
            listViewBtn.classList.remove('text-white');
            listViewBtn.classList.add('text-secondary-600', 'dark:text-secondary-400');
          } else {
            viewIndicator.style.transform = 'translateX(100%)';
            listViewBtn.classList.remove('text-secondary-600', 'dark:text-secondary-400');
            listViewBtn.classList.add('text-white');
            gridViewBtn.classList.remove('text-white');
            gridViewBtn.classList.add('text-secondary-600', 'dark:text-secondary-400');
          }
          this.isGridView = isGrid;
          this.renderResources();
        };

        gridViewBtn.addEventListener('click', () => setActiveView(true));
        listViewBtn.addEventListener('click', () => setActiveView(false));
      }
      
      // Filter buttons
      document.querySelectorAll('.filter-btn').forEach(btn => {
        btn.addEventListener('click', (e) => {
          this.setActiveFilter(e.target);
          this.currentFilter = e.target.dataset.filter;
          this.filterAndSort();
        });
      });
      
      // Share buttons
      document.querySelectorAll('.share-btn').forEach(btn => {
        btn.addEventListener('click', (e) => {
          e.preventDefault();
          this.shareResource(btn);
        });
      });

      // Keyboard shortcut for search (⌘K or Ctrl+K)
      document.addEventListener('keydown', (e) => {
        if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
          e.preventDefault();
          if (searchModal && searchModal.classList.contains('hidden')) {
            searchTrigger?.click();
          }
        }
      });
    }
    
    setActiveFilter(activeBtn) {
      // Remove active state from all filter buttons
      document.querySelectorAll('.filter-btn').forEach(btn => {
        btn.classList.remove('active');
        if (btn.dataset.filter === 'all') {
          // Reset "All Resources" button to inactive state
          btn.classList.remove('bg-gradient-to-r', 'from-primary-500', 'to-primary-600', 'text-white', 'font-semibold', 'shadow-lg', 'shadow-primary-500/25', 'hover:from-primary-600', 'hover:to-primary-700');
          btn.classList.add('text-secondary-700', 'dark:text-secondary-300', 'hover:bg-secondary-50', 'dark:hover:bg-secondary-800/50', 'font-medium');
        } else {
          // Reset category buttons to inactive state
          btn.classList.remove('bg-gradient-to-r', 'from-primary-500', 'to-primary-600', 'text-white', 'font-semibold', 'shadow-lg', 'shadow-primary-500/25', 'hover:from-primary-600', 'hover:to-primary-700');
          btn.classList.add('text-secondary-700', 'dark:text-secondary-300', 'hover:bg-secondary-50', 'dark:hover:bg-secondary-800/50', 'font-medium');
        }
      });

      // Set active state for the clicked button
      activeBtn.classList.add('active');
      if (activeBtn.dataset.filter === 'all') {
        // Style active "All Resources" button
        activeBtn.classList.remove('text-secondary-700', 'dark:text-secondary-300', 'hover:bg-secondary-50', 'dark:hover:bg-secondary-800/50', 'font-medium');
        activeBtn.classList.add('bg-gradient-to-r', 'from-primary-500', 'to-primary-600', 'text-white', 'font-semibold', 'shadow-lg', 'shadow-primary-500/25', 'hover:from-primary-600', 'hover:to-primary-700');
      } else {
        // Style active category button
        activeBtn.classList.remove('text-secondary-700', 'dark:text-secondary-300', 'hover:bg-secondary-50', 'dark:hover:bg-secondary-800/50', 'font-medium');
        activeBtn.classList.add('bg-gradient-to-r', 'from-primary-500', 'to-primary-600', 'text-white', 'font-semibold', 'shadow-lg', 'shadow-primary-500/25', 'hover:from-primary-600', 'hover:to-primary-700');
      }
    }
    
    filterAndSort() {
      // Filter resources
      this.filteredResources = this.resources.filter(resource => {
        const matchesSearch = !this.searchQuery || 
          resource.title.includes(this.searchQuery) ||
          resource.category.includes(this.searchQuery) ||
          resource.tags.some(tag => tag.includes(this.searchQuery));
          
        const matchesFilter = this.currentFilter === 'all' || 
          resource.category === this.currentFilter;
          
        return matchesSearch && matchesFilter;
      });
      
      // Sort resources
      this.filteredResources.sort((a, b) => {
        switch (this.currentSort) {
          case 'title':
            return a.title.localeCompare(b.title);
          case 'category':
            return a.category.localeCompare(b.category);
          case 'recent':
            return 0;
          default:
            return 0;
        }
      });
      
      this.renderResources();
    }
    
    renderResources() {
      const container = document.getElementById('resources-container');
      const noResults = document.getElementById('no-results');
      
      if (!container) return;
      
      // Update container layout based on view mode
      if (this.isGridView) {
        container.className = 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6';
      } else {
        container.className = 'magazine-layout space-y-6';
      }
      
      // Hide all resources first
      this.resources.forEach(resource => {
        resource.element.style.display = 'none';
      });
      
      if (this.filteredResources.length === 0) {
        noResults?.classList.remove('hidden');
      } else {
        noResults?.classList.add('hidden');
        
        // Show filtered resources
        this.filteredResources.forEach((resource, index) => {
          resource.element.style.display = 'block';
          resource.element.style.order = index;
          
          // Add stagger animation
          resource.element.style.animationDelay = `${index * 50}ms`;
          resource.element.classList.add('animate-slide-up');
        });
      }
    }
    
    toggleView() {
      const container = document.getElementById('resources-container');
      const viewToggle = document.getElementById('view-toggle');
      
      if (!container || !viewToggle) return;
      
      this.isGridView = !this.isGridView;
      
      if (this.isGridView) {
        container.className = 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6';
        viewToggle.innerHTML = `
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"></path>
          </svg>
          Cards
        `;
      } else {
        container.className = 'magazine-layout space-y-6';
        viewToggle.innerHTML = `
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
          </svg>
          Magazine
        `;
      }
    }
    
    shareResource(btn) {
      const url = btn.dataset.url;
      const title = btn.dataset.title;
      
      if (navigator.share) {
        navigator.share({
          title: title,
          url: url
        }).catch(err => console.log('Error sharing:', err));
      } else if (navigator.clipboard) {
        navigator.clipboard.writeText(url).then(() => {
          this.showToast('Link copied to clipboard!');
        });
      } else {
        this.showToast('Link: ' + url);
      }
    }
    
    showToast(message) {
      const toast = document.createElement('div');
      toast.className = 'fixed bottom-4 right-4 bg-secondary-800 text-white px-6 py-3 rounded-xl shadow-lg z-50 transform translate-y-full opacity-0 transition-all duration-300';
      toast.textContent = message;
      document.body.appendChild(toast);
      
      setTimeout(() => {
        toast.classList.remove('translate-y-full', 'opacity-0');
      }, 100);
      
      setTimeout(() => {
        toast.classList.add('translate-y-full', 'opacity-0');
        setTimeout(() => toast.remove(), 300);
      }, 3000);
    }
    
  }
  
  function showToast(message) {
    const toast = document.createElement('div');
    toast.className = 'fixed bottom-4 right-4 bg-secondary-800 text-white px-6 py-3 rounded-xl shadow-lg z-50 transform translate-y-full opacity-0 transition-all duration-300';
    toast.textContent = message;
    document.body.appendChild(toast);
    
    setTimeout(() => {
      toast.classList.remove('translate-y-full', 'opacity-0');
    }, 100);
    
    setTimeout(() => {
      toast.classList.add('translate-y-full', 'opacity-0');
      setTimeout(() => toast.remove(), 300);
    }, 3000);
  }

  // Image loading optimization
  function optimizeImageLoading() {
    const images = document.querySelectorAll('.resource-card img[loading="lazy"]');

    // Add intersection observer for better lazy loading
    if ('IntersectionObserver' in window) {
      const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const img = entry.target;
            img.addEventListener('load', () => {
              img.classList.add('loaded');
            });
            observer.unobserve(img);
          }
        });
      }, {
        rootMargin: '50px 0px',
        threshold: 0.01
      });

      images.forEach(img => imageObserver.observe(img));
    } else {
      // Fallback for browsers without IntersectionObserver
      images.forEach(img => {
        img.addEventListener('load', () => {
          img.classList.add('loaded');
        });
      });
    }
  }

  // Preload critical images
  function preloadCriticalImages() {
    const firstRowImages = document.querySelectorAll('.resource-card:nth-child(-n+3) img');
    firstRowImages.forEach(img => {
      if (img.dataset.src) {
        img.src = img.dataset.src;
      }
    });
  }

  // Initialize when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      new ResourcesManager();
      optimizeImageLoading();
      preloadCriticalImages();
    });
  } else {
    new ResourcesManager();
    optimizeImageLoading();
    preloadCriticalImages();
  }

  // Re-initialize on page navigation (for Astro)
  document.addEventListener('astro:page-load', () => {
    new ResourcesManager();
    optimizeImageLoading();
    preloadCriticalImages();
  });

</script>