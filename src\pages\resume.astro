---
import Layout from '../layouts/Layout.astro';
import { siteConfig } from '../config/site';
---

<Layout title={`Resume | ${siteConfig.title}`}>
  <section class="resume hero-compact bg-gradient-to-br from-background-light via-background-light-secondary to-secondary-50/40 dark:from-background-dark dark:via-background-dark-secondary dark:to-secondary-950/40 relative overflow-hidden" role="region" aria-labelledby="resume-title">
    <!-- Modern background elements matching Contact page -->
    <div class="absolute inset-0 pointer-events-none">
      <div class="absolute top-20 left-10 w-72 h-72 bg-gradient-to-br from-primary-400/5 to-transparent rounded-full blur-3xl"></div>
      <div class="absolute bottom-20 right-10 w-72 h-72 bg-gradient-to-tl from-accent-400/5 to-transparent rounded-full blur-3xl"></div>
    </div>

    <div class="container-custom relative z-10">
      <div class="hero-title-compact">
        <h1 id="resume-title" class="heading-xl text-secondary-800 dark:text-secondary-200 mb-6 relative">
          Resume
          <span class="absolute bottom-0 left-1/2 transform -translate-x-1/2 -mb-2 w-20 h-1 bg-gradient-to-r from-primary-500 to-accent-500 rounded"></span>
        </h1>
        <p class="text-lg text-secondary-600 dark:text-secondary-400 max-w-2xl mx-auto leading-relaxed">
          Comprehensive overview of my experience, skills, and professional journey
        </p>
      </div>
      
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-12 items-start">
        <div class="lg:col-span-2 space-y-8">
          <!-- Professional Summary with modern styling -->
          <div class="glass-card p-8 rounded-2xl">
            <div class="flex items-center gap-3 mb-6">
              <div class="w-10 h-10 bg-gradient-to-br from-primary-500 to-accent-500 rounded-xl flex items-center justify-center text-white">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
              </div>
              <h2 class="text-2xl font-bold text-secondary-800 dark:text-secondary-200 font-heading">Professional Summary</h2>
            </div>
            <p class="text-secondary-600 dark:text-secondary-400 leading-relaxed text-lg">
              Software developer with experience in building scalable systems and modern web applications. Passionate about clean code, system architecture, and continuous learning. Experienced in full-stack development with a focus on backend technologies and DevOps practices.
            </p>
          </div>
          
          <!-- Experience Areas with enhanced styling -->
          <div class="glass-card p-8 rounded-2xl">
            <div class="flex items-center gap-3 mb-8">
              <div class="w-10 h-10 bg-gradient-to-br from-primary-500 to-accent-500 rounded-xl flex items-center justify-center text-white">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                </svg>
              </div>
              <h2 class="text-2xl font-bold text-secondary-800 dark:text-secondary-200 font-heading">Key Experience Areas</h2>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div class="experience-item group bg-white/90 dark:bg-secondary-800/90 p-6 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-2 border border-primary-300/20 dark:border-primary-600/30">
                <div class="flex items-center gap-3 mb-4">
                  <div class="w-8 h-8 bg-gradient-to-br from-primary-500 to-primary-600 rounded-lg flex items-center justify-center text-white text-sm">⚡</div>
                  <h3 class="text-lg font-semibold text-primary-700 dark:text-primary-300 font-heading">Backend Development</h3>
                </div>
                <p class="text-secondary-600 dark:text-secondary-400 text-sm leading-relaxed">API design, database optimization, microservices architecture</p>
              </div>
              <div class="experience-item group bg-white/90 dark:bg-secondary-800/90 p-6 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-2 border border-success-300/25 dark:border-success-600/35">
                <div class="flex items-center gap-3 mb-4">
                  <div class="w-8 h-8 bg-gradient-to-br from-success-500 to-success-600 rounded-lg flex items-center justify-center text-white text-sm">🏗️</div>
                  <h3 class="text-lg font-semibold text-success-700 dark:text-success-300 font-heading">System Architecture</h3>
                </div>
                <p class="text-secondary-600 dark:text-secondary-400 text-sm leading-relaxed">Scalable system design, performance optimization, cloud infrastructure</p>
              </div>
              <div class="experience-item group bg-white/90 dark:bg-secondary-800/90 p-6 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-2 border border-warning-300/25 dark:border-warning-600/35">
                <div class="flex items-center gap-3 mb-4">
                  <div class="w-8 h-8 bg-gradient-to-br from-warning-500 to-warning-600 rounded-lg flex items-center justify-center text-white text-sm">☁️</div>
                  <h3 class="text-lg font-semibold text-warning-700 dark:text-warning-300 font-heading">DevOps & Automation</h3>
                </div>
                <p class="text-secondary-600 dark:text-secondary-400 text-sm leading-relaxed">CI/CD pipelines, containerization, infrastructure as code</p>
              </div>
              <div class="experience-item group bg-white/90 dark:bg-secondary-800/90 p-6 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-2 border border-primary-300/20 dark:border-primary-600/30">
                <div class="flex items-center gap-3 mb-4">
                  <div class="w-8 h-8 bg-gradient-to-br from-primary-600 to-accent-500 rounded-lg flex items-center justify-center text-white text-sm">👥</div>
                  <h3 class="text-lg font-semibold text-primary-700 dark:text-primary-300 font-heading">Technical Leadership</h3>
                </div>
                <p class="text-secondary-600 dark:text-secondary-400 text-sm leading-relaxed">Code review, mentoring, technical decision making</p>
              </div>
            </div>
          </div>

          <!-- Skills Section -->
          <div class="glass-card p-8 rounded-2xl">
            <div class="flex items-center gap-3 mb-8">
              <div class="w-10 h-10 bg-gradient-to-br from-accent-500 to-primary-500 rounded-xl flex items-center justify-center text-white">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                </svg>
              </div>
              <h2 class="text-2xl font-bold text-secondary-800 dark:text-secondary-200 font-heading">Technical Skills</h2>
            </div>
            
            <div class="grid grid-cols-1 gap-6">
              <!-- Programming Languages -->
              <div class="skill-category">
                <h4 class="text-lg font-semibold text-primary-700 dark:text-primary-300 mb-4 font-heading">Programming Languages</h4>
                <div class="flex flex-wrap gap-2">
                  {["Java", "Python", "JavaScript", "TypeScript", "Go", "SQL"].map(skill => (
                    <span class="skill-tag px-3 py-1.5 bg-gradient-to-r from-primary-400/15 to-primary-300/10 dark:from-primary-500/25 dark:to-primary-400/20 text-primary-700 dark:text-primary-200 text-sm font-medium rounded-full border border-primary-400/25 dark:border-primary-500/30 hover:bg-gradient-to-r hover:from-primary-400/25 hover:to-primary-300/20 dark:hover:from-primary-500/35 dark:hover:to-primary-400/30 transition-all duration-300 hover:scale-105">
                      {skill}
                    </span>
                  ))}
                </div>
              </div>

              <!-- Frameworks & Tools -->
              <div class="skill-category">
                <h4 class="text-lg font-semibold text-secondary-700 dark:text-secondary-200 mb-4 font-heading">Frameworks & Tools</h4>
                <div class="flex flex-wrap gap-2">
                  {["Spring Boot", "Node.js", "React", "Docker", "Kubernetes", "AWS", "PostgreSQL", "Redis"].map(skill => (
                    <span class="skill-tag px-3 py-1.5 bg-gradient-to-r from-secondary-400/15 to-secondary-300/10 dark:from-secondary-500/25 dark:to-secondary-400/20 text-secondary-700 dark:text-secondary-200 text-sm font-medium rounded-full border border-secondary-400/25 dark:border-secondary-500/30 hover:bg-gradient-to-r hover:from-secondary-400/25 hover:to-secondary-300/20 dark:hover:from-secondary-500/35 dark:hover:to-secondary-400/30 transition-all duration-300 hover:scale-105">
                      {skill}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Download Section matching Contact page styling -->
        <div class="space-y-8">
          <div class="glass-card p-6 rounded-2xl">
            <div class="flex items-center gap-3 mb-4">
              <div class="w-10 h-10 bg-gradient-to-br from-primary-500 to-accent-500 rounded-xl flex items-center justify-center text-white">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
              </div>
              <h3 class="text-xl font-semibold text-secondary-800 dark:text-secondary-200">Download Resume</h3>
            </div>
            <p class="text-secondary-600 dark:text-secondary-400 mb-3">Professional resume available soon.</p>
            <div class="coming-soon bg-secondary-50/80 dark:bg-secondary-800/50 border-2 border-dashed border-secondary-300/50 dark:border-secondary-600/50 p-6 rounded-xl">
              <div class="flex items-center justify-center mb-3">
                <div class="w-12 h-12 bg-gradient-to-br from-secondary-400/20 to-accent-400/15 rounded-full flex items-center justify-center">
                  <svg class="w-6 h-6 text-secondary-600 dark:text-secondary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                </div>
              </div>
              <p class="text-secondary-600 dark:text-secondary-400 italic text-sm leading-relaxed">
                PDF download coming soon
              </p>
            </div>
          </div>
          
          <div class="glass-card p-6 rounded-2xl">
            <div class="flex items-center gap-3 mb-4">
              <div class="w-10 h-10 bg-gradient-to-br from-accent-500 to-primary-500 rounded-xl flex items-center justify-center text-white">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
                </svg>
              </div>
              <h3 class="text-xl font-semibold text-secondary-800 dark:text-secondary-200">Quick Links</h3>
            </div>
            <div class="space-y-4">
              <a href="/portfolio" class="group flex items-center justify-center w-12 h-12 bg-secondary-100 dark:bg-secondary-800 hover:bg-primary-100 dark:hover:bg-primary-900/30 rounded-xl transition-all duration-300 hover:-translate-y-1">
                <svg class="w-5 h-5 text-secondary-600 dark:text-secondary-400 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                </svg>
              </a>
              <a href="/contact" class="group flex items-center justify-center w-12 h-12 bg-secondary-100 dark:bg-secondary-800 hover:bg-primary-100 dark:hover:bg-primary-900/30 rounded-xl transition-all duration-300 hover:-translate-y-1">
                <svg class="w-5 h-5 text-secondary-600 dark:text-secondary-400 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                </svg>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</Layout> 