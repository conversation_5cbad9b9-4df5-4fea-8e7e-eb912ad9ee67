/**
 * Utility functions for extracting images from URLs
 */

interface OpenGraphData {
  image?: string;
  title?: string;
  description?: string;
  siteName?: string;
}

/**
 * Extract Open Graph image from a URL
 * This function attempts to fetch the page and extract the og:image meta tag
 */
export async function extractImageFromUrl(url: string): Promise<string | null> {
  try {
    // For build-time, we'll use a service like urlbox.io or similar
    // For now, we'll implement a fallback system based on known domains
    return getImageFromKnownDomain(url);
  } catch (error) {
    console.warn(`Failed to extract image from ${url}:`, error);
    return null;
  }
}

/**
 * Get image from known domains using their favicon or known image patterns
 */
function getImageFromKnownDomain(url: string): string | null {
  try {
    const urlObj = new URL(url);
    const domain = urlObj.hostname.toLowerCase();
    
    // Known domain mappings
    const domainImages: Record<string, string> = {
      // Documentation sites
      'docs.astro.build': 'https://docs.astro.build/favicon.svg',
      'developer.mozilla.org': 'https://developer.mozilla.org/favicon-48x48.png',
      'tailwindcss.com': 'https://tailwindcss.com/favicons/favicon-32x32.png',
      'www.typescriptlang.org': 'https://www.typescriptlang.org/favicon-32x32.png',
      'typescriptlang.org': 'https://www.typescriptlang.org/favicon-32x32.png',
      
      // Design tools
      'www.figma.com': 'https://static.figma.com/app/icon/1/favicon.png',
      'figma.com': 'https://static.figma.com/app/icon/1/favicon.png',
      
      // Development tools
      'github.com': 'https://github.com/favicon.ico',
      'code.visualstudio.com': 'https://code.visualstudio.com/favicon.ico',
      
      // Learning platforms
      'youtube.com': 'https://www.youtube.com/favicon.ico',
      'www.youtube.com': 'https://www.youtube.com/favicon.ico',
      
      // Generic fallbacks for common domains
      'docs.': `https://www.google.com/s2/favicons?domain=${domain}&sz=64`,
      'www.': `https://www.google.com/s2/favicons?domain=${domain}&sz=64`,
    };
    
    // Direct domain match
    if (domainImages[domain]) {
      return domainImages[domain];
    }
    
    // Pattern matching for subdomains
    for (const [pattern, imageUrl] of Object.entries(domainImages)) {
      if (domain.includes(pattern)) {
        return imageUrl;
      }
    }
    
    // Fallback to Google's favicon service
    return `https://www.google.com/s2/favicons?domain=${domain}&sz=64`;
    
  } catch (error) {
    console.warn(`Failed to parse URL ${url}:`, error);
    return null;
  }
}

/**
 * Generate a fallback image URL based on the first letter of the title
 * This creates a simple colored background with the first letter
 */
export function generateFallbackImage(title: string, category: string): string {
  const firstLetter = title.charAt(0).toUpperCase();
  const colors = {
    'documentation': '3B82F6', // blue
    'tools': '10B981', // emerald
    'learning': '8B5CF6', // purple
    'design': 'F59E0B', // amber
    'default': '6B7280' // gray
  };
  
  const color = colors[category.toLowerCase() as keyof typeof colors] || colors.default;
  
  // Using a simple SVG data URL for the fallback
  const svg = `
    <svg width="64" height="64" xmlns="http://www.w3.org/2000/svg">
      <rect width="64" height="64" fill="#${color}"/>
      <text x="32" y="40" font-family="Arial, sans-serif" font-size="24" font-weight="bold" text-anchor="middle" fill="white">${firstLetter}</text>
    </svg>
  `;
  
  return `data:image/svg+xml;base64,${btoa(svg)}`;
}

/**
 * Get the best available image for a resource
 * Priority: manual override > extracted image > fallback
 */
export async function getResourceImage(
  url: string, 
  title: string, 
  category: string, 
  manualImage?: string
): Promise<string> {
  // 1. Use manual override if provided
  if (manualImage) {
    return manualImage;
  }
  
  // 2. Try to extract from URL
  const extractedImage = await extractImageFromUrl(url);
  if (extractedImage) {
    return extractedImage;
  }
  
  // 3. Fallback to generated image
  return generateFallbackImage(title, category);
}
